// routes/AppRouter.tsx
import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import LandingPage from '../pages/LandingPage';
import Dashboard from '../pages/Dashboard';
import SyncLogs from '../pages/SyncLogs';
import InvoiceSync from '../pages/InvoiceSync';
import PaymentSync from '../pages/PaymentSync';
import Settings from '../pages/Settings';

// Protected Route Component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const isAuthenticated = localStorage.getItem('qb_access_token') && localStorage.getItem('qb_realm_id');
  
  if (!isAuthenticated) {
    return <Navigate to="/" replace />;
  }
  
  return <>{children}</>;
};

// Public Route Component (redirects to dashboard if already authenticated)
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const isAuthenticated = localStorage.getItem('qb_access_token') && localStorage.getItem('qb_realm_id');
  
  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }
  
  return <>{children}</>;
};

const AppRouter: React.FC = () => {
  return (
    <Router>
      <Routes>
        {/* Public Routes */}
        <Route 
          path="/" 
          element={
            <PublicRoute>
              <LandingPage />
            </PublicRoute>
          } 
        />
        
        {/* Protected Routes */}
        <Route 
          path="/dashboard" 
          element={
            <ProtectedRoute>
              <Dashboard />
            </ProtectedRoute>
          } 
        />
        
        <Route 
          path="/sync-logs" 
          element={
            <ProtectedRoute>
              <SyncLogs />
            </ProtectedRoute>
          } 
        />
        
        <Route 
          path="/invoice-sync" 
          element={
            <ProtectedRoute>
              <InvoiceSync />
            </ProtectedRoute>
          } 
        />
        
        <Route 
          path="/payment-sync" 
          element={
            <ProtectedRoute>
              <PaymentSync />
            </ProtectedRoute>
          } 
        />
        
        <Route 
          path="/settings" 
          element={
            <ProtectedRoute>
              <Settings />
            </ProtectedRoute>
          } 
        />
        
        {/* Catch all route - redirect to landing or dashboard based on auth */}
        <Route 
          path="*" 
          element={
            localStorage.getItem('qb_access_token') && localStorage.getItem('qb_realm_id') 
              ? <Navigate to="/dashboard" replace />
              : <Navigate to="/" replace />
          } 
        />
      </Routes>
    </Router>
  );
};

export default AppRouter;