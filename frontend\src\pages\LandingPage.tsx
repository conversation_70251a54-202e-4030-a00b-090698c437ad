import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Card, Row, Col, Typography, Space, Alert, Spin, message } from 'antd';
import { 
  FileTextOutlined, 
  DollarOutlined, 
  SyncOutlined, 
  CheckCircleOutlined,
  SafetyOutlined,
  ClockCircleOutlined,
  Bar<PERSON>hartOutlined,
  ReloadOutlined,
  DashboardOutlined,
  RightOutlined
} from '@ant-design/icons';
import { getAuthUrl, handleCallback } from '../api/qboAuth';
import { isAuthenticated, saveAuthData, getCompanyName } from '../utils/auth';

const { Title, Paragraph, Text } = Typography;

const LandingPage = () => {
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Check for existing authentication on component mount
  useEffect(() => {
    const checkAuthStatus = () => {
      if (isAuthenticated()) {
        setIsAuthenticated(true);
        setConnectionStatus('connected');
      }
    };

    checkAuthStatus();

    // Check for OAuth callback parameters in URL
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');
    const realmId = urlParams.get('realmId');
    const error = urlParams.get('error');

    if (error) {
      message.error(`Authentication failed: ${error}`);
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
    } else if (code && state && realmId) {
      handleOAuthCallback(code, state, realmId);
    }
  }, []);

  const handleOAuthCallback = async (code, state, realmId) => {
    setIsConnecting(true);
    try {
      const response = await handleCallback(code, state, realmId);

      if (response.data.status === 'success') {
        const { connection } = response.data.data;
        
        // Store authentication data using utility
        saveAuthData({
          accessToken: 'authenticated', // We don't store actual token for security
          realmId: connection.realmId,
          connectionId: connection.id,
          companyName: connection.companyName || 'Unknown Company'
        });
        
        setIsAuthenticated(true);
        setConnectionStatus('connected');
        message.success('Successfully connected to QuickBooks!');
        
        // Clean up URL
        window.history.replaceState({}, document.title, window.location.pathname);
      }
    } catch (error) {
      console.error('OAuth callback error:', error);
      message.error('Failed to complete QuickBooks authentication');
      setConnectionStatus('error');
    } finally {
      setIsConnecting(false);
    }
  };

  const handleConnect = async () => {
    setIsConnecting(true);
    try {
      const response = await getAuthUrl();
      
      if (response.data.status === 'success') {
        const { authUrl } = response.data.data;
        // Redirect to QuickBooks OAuth page
        window.location.href = authUrl;
      } else {
        message.error('Failed to generate QuickBooks authorization URL');
        setIsConnecting(false);
      }
    } catch (error) {
      console.error('Connection error:', error);
      message.error('Failed to initiate QuickBooks connection');
      setIsConnecting(false);
    }
  };

  const handleGoToDashboard = () => {
    // This would typically use React Router navigation
    window.location.href = '/dashboard';
  };

  const handleViewDashboard = () => {
    // This would typically use React Router navigation
    window.location.href = '/dashboard';
  };

  const features = [
    {
      icon: <SafetyOutlined style={{ fontSize: '24px', color: '#1890ff' }} />,
      title: 'Secure OAuth 2.0 integration with QuickBooks',
    },
    {
      icon: <ClockCircleOutlined style={{ fontSize: '24px', color: '#1890ff' }} />,
      title: 'Real-time invoice and payment synchronization',
    },
    {
      icon: <BarChartOutlined style={{ fontSize: '24px', color: '#1890ff' }} />,
      title: 'Detailed sync logs with status tracking',
    },
    {
      icon: <SyncOutlined style={{ fontSize: '24px', color: '#1890ff' }} />,
      title: 'Bulk sync operations for efficiency',
    },
    {
      icon: <ReloadOutlined style={{ fontSize: '24px', color: '#1890ff' }} />,
      title: 'Error handling and retry capabilities',
    },
    {
      icon: <DashboardOutlined style={{ fontSize: '24px', color: '#1890ff' }} />,
      title: 'User-friendly dashboard interface',
    },
  ];

  return (
    <div style={{ 
      minHeight: '100vh', 
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      padding: '20px 0'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 20px' }}>
        {/* Header */}
        <div style={{ textAlign: 'center', marginBottom: '60px' }}>
          <Title level={1} style={{ 
            fontSize: '48px', 
            marginBottom: '16px',
            background: 'linear-gradient(45deg, #1890ff, #722ed1)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}>
            QuickSync <Text style={{ color: '#1890ff' }}>AP</Text>
          </Title>
          <Paragraph style={{ 
            fontSize: '18px', 
            color: '#666',
            maxWidth: '600px',
            margin: '0 auto 40px',
            lineHeight: '1.6'
          }}>
            Streamline your accounts payable workflow with seamless QuickBooks integration
          </Paragraph>
          
          <Space size="large">
            {!isAuthenticated ? (
              <Button 
                type="primary" 
                size="large"
                icon={<RightOutlined />}
                onClick={handleConnect}
                loading={isConnecting}
                style={{
                  height: '50px',
                  padding: '0 30px',
                  fontSize: '16px',
                  borderRadius: '6px'
                }}
              >
                {isConnecting ? 'Connecting...' : 'Get Started'}
              </Button>
            ) : (
              <Button 
                type="primary" 
                size="large"
                icon={<DashboardOutlined />}
                onClick={handleGoToDashboard}
                style={{
                  height: '50px',
                  padding: '0 30px',
                  fontSize: '16px',
                  borderRadius: '6px'
                }}
              >
                Go to Dashboard
              </Button>
            )}
            
            <Button 
              size="large"
              onClick={handleViewDashboard}
              style={{
                height: '50px',
                padding: '0 30px',
                fontSize: '16px',
                borderRadius: '6px'
              }}
            >
              View Dashboard
            </Button>
          </Space>

          {isAuthenticated && (
            <Alert
              message="QuickBooks Connected Successfully"
              description={`Connected to ${getCompanyName()} - You can now sync your transactions`}
              type="success"
              showIcon
              style={{ marginTop: '20px', maxWidth: '500px', margin: '20px auto 0' }}
            />
          )}
        </div>

        {/* How It Works Section */}
        <div style={{ marginBottom: '80px' }}>
          <Title level={2} style={{ textAlign: 'center', marginBottom: '20px' }}>
            How It Works
          </Title>
          <Paragraph style={{ 
            textAlign: 'center', 
            fontSize: '16px', 
            color: '#666',
            marginBottom: '60px'
          }}>
            Our platform makes it easy to keep your accounting data in sync with QuickBooks
          </Paragraph>

          <Row gutter={[32, 32]} justify="center">
            <Col xs={24} sm={24} md={8}>
              <Card
                style={{ 
                  textAlign: 'center',
                  height: '100%',
                  border: 'none',
                  borderRadius: '12px',
                  boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
                }}
                bodyStyle={{ padding: '40px 20px' }}
              >
                <FileTextOutlined style={{ 
                  fontSize: '48px', 
                  color: '#1890ff',
                  marginBottom: '20px'
                }} />
                <Title level={4} style={{ marginBottom: '16px' }}>
                  1. Connect QuickBooks
                </Title>
                <Paragraph style={{ color: '#666', lineHeight: '1.6' }}>
                  Securely link your QuickBooks Online account with our platform using OAuth 2.0 authentication
                </Paragraph>
              </Card>
            </Col>

            <Col xs={24} sm={24} md={8}>
              <Card
                style={{ 
                  textAlign: 'center',
                  height: '100%',
                  border: 'none',
                  borderRadius: '12px',
                  boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
                }}
                bodyStyle={{ padding: '40px 20px' }}
              >
                <DollarOutlined style={{ 
                  fontSize: '48px', 
                  color: '#52c41a',
                  marginBottom: '20px'
                }} />
                <Title level={4} style={{ marginBottom: '16px' }}>
                  2. Sync Transactions
                </Title>
                <Paragraph style={{ color: '#666', lineHeight: '1.6' }}>
                  Automatically or manually sync invoices and payments between your system and QuickBooks
                </Paragraph>
              </Card>
            </Col>

            <Col xs={24} sm={24} md={8}>
              <Card
                style={{ 
                  textAlign: 'center',
                  height: '100%',
                  border: 'none',
                  borderRadius: '12px',
                  boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
                }}
                bodyStyle={{ padding: '40px 20px' }}
              >
                <SyncOutlined style={{ 
                  fontSize: '48px', 
                  color: '#fa8c16',
                  marginBottom: '20px'
                }} />
                <Title level={4} style={{ marginBottom: '16px' }}>
                  3. Monitor Sync Status
                </Title>
                <Paragraph style={{ color: '#666', lineHeight: '1.6' }}>
                  Track all synchronization activities with a detailed sync log and real-time status updates
                </Paragraph>
              </Card>
            </Col>
          </Row>
        </div>

        {/* Features Section */}
        <Row gutter={[48, 0]} align="middle">
          <Col xs={24} lg={12}>
            <Title level={2} style={{ marginBottom: '24px' }}>
              Key Features
            </Title>
            
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              {features.map((feature, index) => (
                <div key={index} style={{ display: 'flex', alignItems: 'center' }}>
                  <CheckCircleOutlined style={{ 
                    color: '#52c41a', 
                    fontSize: '16px',
                    marginRight: '12px',
                    flexShrink: 0
                  }} />
                  <Text style={{ fontSize: '16px', lineHeight: '1.5' }}>
                    {feature.title}
                  </Text>
                </div>
              ))}
            </Space>

            <div style={{ marginTop: '40px' }}>
              <Button 
                type="primary" 
                size="large"
                icon={<RightOutlined />}
                onClick={!isAuthenticated ? handleConnect : handleGoToDashboard}
                loading={isConnecting}
                style={{
                  height: '50px',
                  padding: '0 30px',
                  fontSize: '16px',
                  borderRadius: '6px'
                }}
              >
                {!isAuthenticated ? 'Connect Your Account' : 'Go to Dashboard'}
              </Button>
            </div>
          </Col>

          <Col xs={24} lg={12}>
            <Card
              style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                border: 'none',
                borderRadius: '16px',
                color: 'white',
                textAlign: 'center'
              }}
              bodyStyle={{ padding: '60px 40px' }}
            >
              <Title level={3} style={{ color: 'white', marginBottom: '20px' }}>
                Ready to streamline your AP workflow?
              </Title>
              <Paragraph style={{ 
                color: 'rgba(255,255,255,0.9)', 
                fontSize: '16px',
                marginBottom: '30px',
                lineHeight: '1.6'
              }}>
                Connect your QuickBooks account and start syncing your transactions in minutes.
              </Paragraph>
              <Button 
                size="large"
                onClick={!isAuthenticated ? handleConnect : handleGoToDashboard}
                loading={isConnecting}
                style={{
                  height: '50px',
                  padding: '0 30px',
                  fontSize: '16px',
                  borderRadius: '6px'
                }}
              >
                {!isAuthenticated ? 'Go to Dashboard' : 'View Dashboard'}
              </Button>
            </Card>
          </Col>
        </Row>

        {/* Loading Spinner for Connection Process */}
        {isConnecting && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(255,255,255,0.8)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1000
          }}>
            <div style={{ textAlign: 'center' }}>
              <Spin size="large" />
              <div style={{ marginTop: '20px', fontSize: '16px', color: '#666' }}>
                Connecting to QuickBooks...
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LandingPage;